rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد للنصوص المحفوظة
    match /savedTexts/{document} {
      // السماح للمستخدمين المسجلين بقراءة وكتابة نصوصهم فقط
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      // السماح بإنشاء نص جديد للمستخدمين المسجلين
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // منع الوصول لأي مجموعات أخرى
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
