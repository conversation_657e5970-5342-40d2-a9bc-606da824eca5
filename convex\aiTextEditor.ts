"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";
import OpenAI from "openai";

const openai = new OpenAI({
  baseURL: process.env.CONVEX_OPENAI_BASE_URL,
  apiKey: process.env.CONVEX_OPENAI_API_KEY,
});

export const analyzeAndCorrectText = action({
  args: { 
    text: v.string(),
    correctionType: v.optional(v.string()) // "grammar", "style", "comprehensive"
  },
  handler: async (ctx, args) => {
    if (!args.text.trim()) {
      return {
        correctedText: "",
        suggestions: [],
        analysis: {
          grammarErrors: 0,
          styleImprovements: 0,
          readabilityScore: 0
        }
      };
    }

    const correctionType = args.correctionType || "comprehensive";
    
    let systemPrompt = "";
    
    switch (correctionType) {
      case "grammar":
        systemPrompt = `أنت محرر نصوص عربي متخصص في التصحيح النحوي والإملائي. مهمتك:
1. تصحيح الأخطاء النحوية والإملائية
2. ضبط علامات الترقيم
3. تصحيح التشكيل إذا لزم الأمر
4. الحفاظ على المعنى الأصلي والأسلوب

قم بإرجاع النص المصحح فقط دون إضافات أو تعليقات.`;
        break;
        
      case "style":
        systemPrompt = `أنت محرر أدبي متخصص في تحسين الأسلوب العربي. مهمتك:
1. تحسين الصياغة والأسلوب
2. اختيار كلمات أكثر دقة وجمالاً
3. تحسين تدفق النص وانسيابيته
4. الحفاظ على المعنى الأصلي

قم بإرجاع النص المحسن فقط دون إضافات أو تعليقات.`;
        break;
        
      default: // comprehensive
        systemPrompt = `أنت محرر نصوص عربي خبير متخصص في التحرير الشامل. مهمتك:
1. تصحيح جميع الأخطاء النحوية والإملائية
2. تحسين الأسلوب والصياغة
3. ضبط علامات الترقيم والتشكيل
4. تحسين تدفق النص وانسيابيته
5. اختيار كلمات أكثر دقة ووضوحاً
6. الحفاظ على المعنى الأصلي والهوية الأدبية للنص

قم بإرجاع النص المحرر والمحسن فقط دون إضافات أو تعليقات.`;
    }

    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: args.text }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const correctedText = response.choices[0].message.content || args.text;

      // تحليل إضافي للنص
      const analysisResponse = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `أنت محلل نصوص عربي. قم بتحليل النص وإرجاع تقرير مختصر بالتنسيق التالي:
الأخطاء النحوية: [عدد]
التحسينات الأسلوبية: [عدد]
مستوى القراءة: [ممتاز/جيد/متوسط/ضعيف]
ملاحظات: [ملاحظة مختصرة]`
          },
          {
            role: "user",
            content: `النص الأصلي: ${args.text}\n\nالنص المحرر: ${correctedText}`
          }
        ],
        temperature: 0.2,
        max_tokens: 300
      });

      const analysisText = analysisResponse.choices[0].message.content || "";
      
      // استخراج المعلومات من التحليل
      const grammarErrors = extractNumber(analysisText, "الأخطاء النحوية") || 0;
      const styleImprovements = extractNumber(analysisText, "التحسينات الأسلوبية") || 0;
      const readabilityMatch = analysisText.match(/مستوى القراءة:\s*(ممتاز|جيد|متوسط|ضعيف)/);
      const readabilityScore = getReadabilityScore(readabilityMatch?.[1] || "متوسط");

      return {
        correctedText,
        analysis: {
          grammarErrors,
          styleImprovements,
          readabilityScore,
          notes: analysisText
        },
        suggestions: generateSuggestions(args.text, correctedText)
      };

    } catch (error) {
      console.error("Error in AI text correction:", error);
      throw new Error("حدث خطأ أثناء تحليل النص. يرجى المحاولة مرة أخرى.");
    }
  },
});

export const generateWritingSuggestions = action({
  args: { 
    text: v.string(),
    context: v.optional(v.string()) // "novel", "article", "essay", "letter"
  },
  handler: async (ctx, args) => {
    const context = args.context || "general";
    
    const contextPrompts = {
      novel: "كتابة رواية أدبية",
      article: "كتابة مقال صحفي",
      essay: "كتابة مقال أكاديمي",
      letter: "كتابة رسالة رسمية",
      general: "كتابة عامة"
    };

    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `أنت مستشار كتابة عربي متخصص في ${contextPrompts[context as keyof typeof contextPrompts]}. 
قدم 3-5 اقتراحات محددة لتحسين النص المرفق. اجعل الاقتراحات عملية ومفيدة.
اكتب كل اقتراح في سطر منفصل يبدأ بـ "•"`
          },
          { role: "user", content: args.text }
        ],
        temperature: 0.4,
        max_tokens: 500
      });

      const suggestionsText = response.choices[0].message.content || "";
      const suggestions = suggestionsText
        .split('\n')
        .filter((line: string) => line.trim().startsWith('•'))
        .map((line: string) => line.replace('•', '').trim());

      return suggestions;

    } catch (error) {
      console.error("Error generating writing suggestions:", error);
      return ["حدث خطأ أثناء توليد الاقتراحات"];
    }
  },
});

function extractNumber(text: string, label: string): number {
  const regex = new RegExp(`${label}:\\s*(\\d+)`);
  const match = text.match(regex);
  return match ? parseInt(match[1]) : 0;
}

function getReadabilityScore(level: string): number {
  const scores = {
    "ممتاز": 95,
    "جيد": 80,
    "متوسط": 65,
    "ضعيف": 40
  };
  return scores[level as keyof typeof scores] || 65;
}

function generateSuggestions(originalText: string, correctedText: string): string[] {
  const suggestions = [];
  
  if (originalText.length !== correctedText.length) {
    suggestions.push("تم تحسين طول النص وتنظيمه");
  }
  
  if (originalText !== correctedText) {
    suggestions.push("تم تصحيح الأخطاء اللغوية والنحوية");
    suggestions.push("تم تحسين الأسلوب والصياغة");
  }
  
  return suggestions;
}
