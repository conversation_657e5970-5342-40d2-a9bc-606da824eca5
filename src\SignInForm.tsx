import { useState } from "react";
import { toast } from "sonner";
import { signInWithEmail, signUpWithEmail, resetPassword } from "./firebase/auth";

export function SignInForm() {
  const [flow, setFlow] = useState<"signIn" | "signUp" | "resetPassword">("signIn");
  const [submitting, setSubmitting] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [displayName, setDisplayName] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      if (flow === "resetPassword") {
        const result = await resetPassword(email);
        if (result.success) {
          toast.success("تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني");
          setFlow("signIn");
        } else {
          toast.error(result.error || "حدث خطأ أثناء إرسال رابط إعادة التعيين");
        }
      } else if (flow === "signUp") {
        const result = await signUpWithEmail(email, password, displayName);
        if (result.success) {
          toast.success("تم إنشاء الحساب بنجاح!");
        } else {
          toast.error(result.error || "حدث خطأ أثناء إنشاء الحساب");
        }
      } else {
        const result = await signInWithEmail(email, password);
        if (result.success) {
          toast.success("تم تسجيل الدخول بنجاح!");
        } else {
          toast.error(result.error || "حدث خطأ أثناء تسجيل الدخول");
        }
      }
    } catch (error) {
      toast.error("حدث خطأ غير متوقع");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="w-full">
      <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
        {flow === "signUp" && (
          <input
            className="auth-input-field"
            type="text"
            value={displayName}
            onChange={(e) => setDisplayName(e.target.value)}
            placeholder="الاسم الكامل"
            required
          />
        )}

        <input
          className="auth-input-field"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="البريد الإلكتروني"
          required
        />

        {flow !== "resetPassword" && (
          <input
            className="auth-input-field"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="كلمة المرور"
            required
          />
        )}

        <button className="auth-button" type="submit" disabled={submitting}>
          {submitting ? "جاري المعالجة..." :
           flow === "signIn" ? "تسجيل الدخول" :
           flow === "signUp" ? "إنشاء حساب" :
           "إرسال رابط إعادة التعيين"}
        </button>
        <div className="text-center text-sm text-secondary dark:text-gray-400 space-y-2">
          {flow === "resetPassword" ? (
            <div>
              <span>تذكرت كلمة المرور؟ </span>
              <button
                type="button"
                className="text-primary hover:text-primary-hover hover:underline font-medium cursor-pointer dark:text-blue-400 dark:hover:text-blue-300"
                onClick={() => setFlow("signIn")}
              >
                تسجيل الدخول
              </button>
            </div>
          ) : (
            <div>
              <span>
                {flow === "signIn"
                  ? "ليس لديك حساب؟ "
                  : "لديك حساب بالفعل؟ "}
              </span>
              <button
                type="button"
                className="text-primary hover:text-primary-hover hover:underline font-medium cursor-pointer dark:text-blue-400 dark:hover:text-blue-300"
                onClick={() => setFlow(flow === "signIn" ? "signUp" : "signIn")}
              >
                {flow === "signIn" ? "إنشاء حساب جديد" : "تسجيل الدخول"}
              </button>
            </div>
          )}

          {flow === "signIn" && (
            <div>
              <button
                type="button"
                className="text-orange-600 hover:text-orange-700 hover:underline font-medium cursor-pointer dark:text-orange-400 dark:hover:text-orange-300"
                onClick={() => setFlow("resetPassword")}
              >
                نسيت كلمة المرور؟
              </button>
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
