import { 
  collection, 
  addDoc, 
  getDocs, 
  deleteDoc, 
  doc, 
  query, 
  where, 
  orderBy,
  Timestamp 
} from "firebase/firestore";
import { db } from "./config";

export interface SavedText {
  id: string;
  title: string;
  originalText: string;
  correctedText?: string;
  userId: string;
  createdAt: Timestamp;
  type: 'spellcheck' | 'ai-editor';
}

// حفظ نص جديد
export const saveText = async (
  title: string,
  originalText: string,
  correctedText: string,
  userId: string,
  type: 'spellcheck' | 'ai-editor' = 'spellcheck'
): Promise<{ success: boolean; id?: string; error?: string }> => {
  try {
    const docRef = await addDoc(collection(db, "savedTexts"), {
      title,
      originalText,
      correctedText,
      userId,
      type,
      createdAt: Timestamp.now()
    });
    
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error("Error saving text:", error);
    return { success: false, error: "فشل في حفظ النص" };
  }
};

// جلب النصوص المحفوظة للمستخدم
export const getUserTexts = async (userId: string): Promise<SavedText[]> => {
  try {
    const q = query(
      collection(db, "savedTexts"),
      where("userId", "==", userId),
      orderBy("createdAt", "desc")
    );
    
    const querySnapshot = await getDocs(q);
    const texts: SavedText[] = [];
    
    querySnapshot.forEach((doc) => {
      texts.push({
        id: doc.id,
        ...doc.data()
      } as SavedText);
    });
    
    return texts;
  } catch (error) {
    console.error("Error fetching texts:", error);
    return [];
  }
};

// حذف نص محفوظ
export const deleteText = async (textId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    await deleteDoc(doc(db, "savedTexts", textId));
    return { success: true };
  } catch (error) {
    console.error("Error deleting text:", error);
    return { success: false, error: "فشل في حذف النص" };
  }
};
