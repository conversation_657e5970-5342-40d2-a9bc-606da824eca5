# 🔥 تفعيل Firebase Authentication

## خطوات مطلوبة في Firebase Console:

### 1. تفعيل Authentication:
1. اذهب إلى https://console.firebase.google.com/project/websate-6ffda
2. من القائمة الجانبية، اختر **"Authentication"**
3. اضغط **"Get started"**

### 2. تفعيل Email/Password:
1. اذهب إلى تبويب **"Sign-in method"**
2. اضغط على **"Email/Password"**
3. فعّل **"Email/Password"**
4. فعّل **"Email link (passwordless sign-in)"** (اختياري)
5. احفظ التغييرات

### 3. إعداد قالب البريد الإلكتروني:
1. اذهب إلى تبويب **"Templates"**
2. اختر **"Password reset"**
3. عدّل الإعدادات كما يلي:

#### **Sender name (اسم المرسل):**
```
محرر النصوص الذكي
```

#### **From (من):**
```
<EMAIL>
```

#### **Reply to (الرد إلى):**
```
noreply
```

#### **Subject (الموضوع):**
```
إعادة تعيين كلمة المرور - محرر النصوص الذكي
```

#### **Message (الرسالة):**
```
مرحباً،

تلقينا طلباً لإعادة تعيين كلمة المرور لحسابك في محرر النصوص الذكي.

البريد الإلكتروني: %EMAIL%

اضغط على الرابط أدناه لإعادة تعيين كلمة المرور:
%LINK%

هذا الرابط صالح لمدة ساعة واحدة فقط.

إذا لم تطلب إعادة تعيين كلمة المرور، يمكنك تجاهل هذا البريد الإلكتروني بأمان.

مع أطيب التحيات،
فريق محرر النصوص الذكي

---
هذا بريد إلكتروني تلقائي، يرجى عدم الرد عليه.
```

### 4. إعداد النطاق المعتمد:
1. اذهب إلى تبويب **"Settings"**
2. في قسم **"Authorized domains"**
3. أضف: `websate-6ffda.web.app`
4. أضف: `websate-6ffda.firebaseapp.com`
5. أضف: `localhost` (للتطوير المحلي)

### 5. تفعيل Firestore:
1. اذهب إلى **"Firestore Database"**
2. اضغط **"Create database"**
3. اختر **"Start in test mode"**
4. اختر المنطقة الجغرافية المناسبة

### 6. اختبار الوظيفة:
1. اذهب إلى https://websate-6ffda.web.app
2. اضغط "نسيت كلمة المرور؟"
3. أدخل بريد إلكتروني صحيح
4. تحقق من صندوق الوارد والرسائل غير المرغوب فيها
5. اضغط على الرابط في البريد الإلكتروني
6. أدخل كلمة المرور الجديدة

### 7. استكشاف الأخطاء:
- تأكد من تفعيل Email/Password في Sign-in method
- تحقق من إعدادات النطاق المعتمد
- تأكد من صحة البريد الإلكتروني المدخل
- تحقق من صندوق الرسائل غير المرغوب فيها
