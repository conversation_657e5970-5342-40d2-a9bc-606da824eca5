# 🔥 تفعيل Firebase Authentication

## خطوات مطلوبة في Firebase Console:

### 1. تفعيل Authentication:
1. اذهب إلى https://console.firebase.google.com/project/websate-6ffda
2. من القائمة الجانبية، اختر **"Authentication"**
3. اضغط **"Get started"**

### 2. تفعيل Email/Password:
1. اذهب إلى تبويب **"Sign-in method"**
2. اضغط على **"Email/Password"**
3. فعّل **"Email/Password"**
4. فعّل **"Email link (passwordless sign-in)"** (اختياري)
5. احفظ التغييرات

### 3. إعداد قالب البريد الإلكتروني:
1. اذهب إلى تبويب **"Templates"**
2. اختر **"Password reset"**
3. عدّل القالب ليكون باللغة العربية:

**الموضوع**: إعادة تعيين كلمة المرور - محرر النصوص الذكي
**المحتوى**:
```
مرحباً،

تلقينا طلباً لإعادة تعيين كلمة المرور لحسابك في محرر النصوص الذكي.

اضغط على الرابط أدناه لإعادة تعيين كلمة المرور:
%LINK%

إذا لم تطلب إعادة تعيين كلمة المرور، يمكنك تجاهل هذا البريد الإلكتروني.

شكراً لك،
فريق محرر النصوص الذكي
```

### 4. إعداد النطاق المعتمد:
1. اذهب إلى تبويب **"Settings"**
2. في قسم **"Authorized domains"**
3. أضف: `websate-6ffda.web.app`
4. أضف: `websate-6ffda.firebaseapp.com`

### 5. تفعيل Firestore:
1. اذهب إلى **"Firestore Database"**
2. اضغط **"Create database"**
3. اختر **"Start in test mode"**
4. اختر المنطقة الجغرافية المناسبة
