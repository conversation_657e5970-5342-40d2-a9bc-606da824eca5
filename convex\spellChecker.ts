import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// قاموس الكلمات العربية الصحيحة (عينة محسنة)
const arabicDictionary = new Set([
  "السلام", "عليكم", "ورحمة", "الله", "وبركاته", "أهلا", "وسهلا", "مرحبا",
  "كيف", "حالك", "الحمد", "لله", "بخير", "شكرا", "عفوا", "من", "فضلك",
  "الكتاب", "المدرسة", "الطالب", "الطالبة", "المعلم", "المعلمة", "الدرس", "الواجب", "الامتحان",
  "البيت", "الأسرة", "الوالد", "الوالدة", "الأخ", "الأخت", "الجد", "الجدة",
  "الطعام", "الماء", "الخبز", "الفاكهة", "الخضار", "اللحم", "السمك",
  "الصباح", "المساء", "الليل", "النهار", "اليوم", "الأمس", "غدا",
  "الشمس", "القمر", "النجوم", "السماء", "الأرض", "البحر", "الجبل",
  "القراءة", "الكتابة", "الحساب", "العلوم", "التاريخ", "الجغرافيا",
  "الرياضة", "كرة", "القدم", "السباحة", "الجري", "اللعب", "المرح",
  "الصحة", "المرض", "الطبيب", "الدواء", "المستشفى", "العلاج",
  "العمل", "الوظيفة", "المكتب", "الشركة", "الموظف", "المدير",
  "السفر", "الطائرة", "القطار", "السيارة", "الطريق", "المطار",
  "الحب", "الصداقة", "الزواج", "الأطفال", "السعادة", "الفرح",
  "الدين", "الصلاة", "الصوم", "الحج", "الزكاة", "المسجد",
  "الوطن", "البلد", "المدينة", "القرية", "الشارع", "الحي",
  // إضافة المزيد من الكلمات الشائعة
  "هذا", "هذه", "ذلك", "تلك", "التي", "الذي", "التي", "اللذان", "اللتان",
  "في", "على", "إلى", "من", "عن", "مع", "بعد", "قبل", "أثناء", "خلال",
  "كان", "كانت", "يكون", "تكون", "سوف", "قد", "لقد", "ربما", "نعم", "لا",
  "جميل", "جميلة", "كبير", "كبيرة", "صغير", "صغيرة", "طويل", "طويلة", "قصير", "قصيرة"
]);

// اقتراحات للأخطاء الشائعة (محسنة)
const commonCorrections: Record<string, string[]> = {
  "السلم": ["السلام"],
  "الكتب": ["الكتاب"],
  "المدرسه": ["المدرسة"],
  "الطالبه": ["الطالبة"],
  "المعلمه": ["المعلمة"],
  "الاسره": ["الأسرة"],
  "الوالده": ["الوالدة"],
  "الاخ": ["الأخ"],
  "الاخت": ["الأخت"],
  "الطعم": ["الطعام"],
  "المياه": ["الماء"],
  "الفواكه": ["الفاكهة"],
  "الخضروات": ["الخضار"],
  "الصبح": ["الصباح"],
  "المسا": ["المساء"],
  "الليله": ["الليل"],
  "النهر": ["النهار"],
  "امس": ["الأمس"],
  "غداً": ["غدا"],
  "الشمش": ["الشمس"],
  "النجم": ["النجوم"],
  "السما": ["السماء"],
  "الارض": ["الأرض"],
  "البحار": ["البحر"],
  "الجبال": ["الجبل"],
  "القرائة": ["القراءة"],
  "الكتابه": ["الكتابة"],
  "الحسب": ["الحساب"],
  "العلم": ["العلوم"],
  "التريخ": ["التاريخ"],
  "الجغرفيا": ["الجغرافيا"],
  "الرياضه": ["الرياضة"],
  "كره": ["كرة"],
  "القدام": ["القدم"],
  "السبحة": ["السباحة"],
  "الجرى": ["الجري"],
  "اللعبة": ["اللعب"],
  "المرحة": ["المرح"],
  "الصحه": ["الصحة"],
  "المرضى": ["المرض"],
  "الطبيبة": ["الطبيب"],
  "الدوا": ["الدواء"],
  "المستشفي": ["المستشفى"],
  "العلج": ["العلاج"],
  "العمال": ["العمل"],
  "الوظيفه": ["الوظيفة"],
  "المكتبة": ["المكتب"],
  "الشركه": ["الشركة"],
  "الموظفة": ["الموظف"],
  "المديرة": ["المدير"],
  "السفار": ["السفر"],
  "الطياره": ["الطائرة"],
  "القطارات": ["القطار"],
  "السياره": ["السيارة"],
  "الطرق": ["الطريق"],
  "المطارات": ["المطار"],
  "الحبة": ["الحب"],
  "الصداقه": ["الصداقة"],
  "الزوج": ["الزواج"],
  "الطفل": ["الأطفال"],
  "السعاده": ["السعادة"],
  "الفرحة": ["الفرح"],
  "الدينة": ["الدين"],
  "الصلوة": ["الصلاة"],
  "الصيم": ["الصوم"],
  "الحجة": ["الحج"],
  "الزكوة": ["الزكاة"],
  "المسجدة": ["المسجد"],
  "الوطان": ["الوطن"],
  "البلاد": ["البلد"],
  "المدينه": ["المدينة"],
  "القريه": ["القرية"],
  "الشوارع": ["الشارع"],
  "الاحياء": ["الحي"],
  // إضافة المزيد من التصحيحات الشائعة
  "هاذا": ["هذا"],
  "هاذه": ["هذه"],
  "ذالك": ["ذلك"],
  "تالك": ["تلك"],
  "اللذى": ["الذي"],
  "اللتى": ["التي"],
  "فى": ["في"],
  "علي": ["على"],
  "الي": ["إلى"],
  "عن": ["عن"],
  "معا": ["مع"],
  "بعدا": ["بعد"],
  "قبلا": ["قبل"],
  "اثناء": ["أثناء"],
  "خلالا": ["خلال"]
};

function generateSuggestions(word: string): string[] {
  const cleanWord = word.replace(/[.,!?;:]/g, '');
  
  if (commonCorrections[cleanWord]) {
    return commonCorrections[cleanWord];
  }
  
  const suggestions: string[] = [];
  
  for (const dictWord of arabicDictionary) {
    if (calculateSimilarity(cleanWord, dictWord) > 0.6) {
      suggestions.push(dictWord);
    }
  }
  
  const commonMistakes = [
    { from: 'ه', to: 'ة' },
    { from: 'ة', to: 'ه' },
    { from: 'ا', to: 'أ' },
    { from: 'أ', to: 'ا' },
    { from: 'ي', to: 'ى' },
    { from: 'ى', to: 'ي' },
    { from: 'ء', to: 'ئ' },
    { from: 'ئ', to: 'ء' }
  ];
  
  for (const mistake of commonMistakes) {
    const corrected = cleanWord.replace(new RegExp(mistake.from, 'g'), mistake.to);
    if (arabicDictionary.has(corrected) && !suggestions.includes(corrected)) {
      suggestions.push(corrected);
    }
  }
  
  return suggestions.slice(0, 5);
}

function calculateSimilarity(word1: string, word2: string): number {
  const len1 = word1.length;
  const len2 = word2.length;
  const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(null));
  
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;
  
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (word1[i - 1] === word2[j - 1]) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  const maxLen = Math.max(len1, len2);
  return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
}

function isArabicWord(word: string): boolean {
  const arabicRegex = /[\u0600-\u06FF]/;
  return arabicRegex.test(word);
}

export const checkSpelling = query({
  args: { text: v.string() },
  handler: async (ctx, args) => {
    const words = args.text.split(/\s+/);
    const errors: Array<{
      word: string;
      position: number;
      suggestions: string[];
      startIndex: number;
      endIndex: number;
      confidence?: number;
      source?: string;
    }> = [];
    
    let currentIndex = 0;
    
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      const cleanWord = word.replace(/[.,!?;:]/g, '');
      
      const wordStartIndex = args.text.indexOf(word, currentIndex);
      const wordEndIndex = wordStartIndex + word.length;
      currentIndex = wordEndIndex;
      
      if (isArabicWord(cleanWord) && cleanWord.length > 1 && !arabicDictionary.has(cleanWord)) {
        const suggestions = generateSuggestions(cleanWord);
        if (suggestions.length > 0) {
          errors.push({
            word: cleanWord,
            position: i,
            suggestions,
            startIndex: wordStartIndex,
            endIndex: wordEndIndex,
            confidence: 0.7,
            source: "local"
          });
        }
      }
    }
    
    return errors;
  },
});

export const saveText = mutation({
  args: {
    originalText: v.string(),
    correctedText: v.string(),
    title: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("يجب تسجيل الدخول لحفظ النص");
    }
    
    return await ctx.db.insert("texts", {
      userId,
      originalText: args.originalText,
      correctedText: args.correctedText,
      title: args.title,
    });
  },
});

export const getUserTexts = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }
    
    return await ctx.db
      .query("texts")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .take(20);
  },
});

export const deleteText = mutation({
  args: { textId: v.id("texts") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("يجب تسجيل الدخول لحذف النص");
    }
    
    const text = await ctx.db.get(args.textId);
    if (!text || text.userId !== userId) {
      throw new Error("غير مسموح بحذف هذا النص");
    }
    
    await ctx.db.delete(args.textId);
  },
});
