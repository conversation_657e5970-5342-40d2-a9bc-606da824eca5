/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as aiTextEditor from "../aiTextEditor.js";
import type * as auth from "../auth.js";
import type * as http from "../http.js";
import type * as microsoftSpellCheck from "../microsoftSpellCheck.js";
import type * as router from "../router.js";
import type * as spellChecker from "../spellChecker.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  aiTextEditor: typeof aiTextEditor;
  auth: typeof auth;
  http: typeof http;
  microsoftSpellCheck: typeof microsoftSpellCheck;
  router: typeof router;
  spellChecker: typeof spellChecker;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
