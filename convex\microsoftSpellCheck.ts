"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";

interface MicrosoftSpellCheckResult {
  flaggedTokens: Array<{
    offset: number;
    token: string;
    type: string;
    suggestions: Array<{
      suggestion: string;
      score: number;
    }>;
  }>;
}

export const checkWithMicrosoft = action({
  args: { 
    text: v.string(),
    language: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const apiKey = process.env.MICROSOFT_COGNITIVE_SERVICES_KEY;
    
    if (!apiKey) {
      console.warn("Microsoft Cognitive Services API key not found, using local spell checker only");
      return null;
    }

    try {
      const endpoint = "https://api.cognitive.microsoft.com/bing/v7.0/spellcheck";
      const language = args.language || "ar";
      
      const params = new URLSearchParams({
        text: args.text,
        mode: "spell",
        mkt: language === "ar" ? "ar-SA" : "en-US"
      });

      const response = await fetch(`${endpoint}?${params}`, {
        method: "POST",
        headers: {
          "Ocp-Apim-Subscription-Key": apiKey,
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });

      if (!response.ok) {
        console.error("Microsoft API error:", response.status, response.statusText);
        return null;
      }

      const result: MicrosoftSpellCheckResult = await response.json();
      
      // تحويل النتائج إلى تنسيق متوافق مع التطبيق
      const errors = result.flaggedTokens.map(token => ({
        word: token.token,
        startIndex: token.offset,
        endIndex: token.offset + token.token.length,
        type: token.type,
        suggestions: token.suggestions
          .sort((a, b) => b.score - a.score)
          .slice(0, 5)
          .map(s => s.suggestion),
        confidence: token.suggestions[0]?.score || 0
      }));

      return errors;
    } catch (error) {
      console.error("Error calling Microsoft Spell Check API:", error);
      return null;
    }
  },
});

export const enhancedSpellCheck = action({
  args: { text: v.string() },
  handler: async (ctx, args) => {
    // الحصول على النتائج من كلا المصدرين
    const [localResults, microsoftResults] = await Promise.all([
      ctx.runQuery(api.spellChecker.checkSpelling, { text: args.text }),
      ctx.runAction(api.microsoftSpellCheck.checkWithMicrosoft, { text: args.text })
    ]);

    // دمج النتائج وإزالة التكرارات
    const combinedErrors = new Map();
    
    // إضافة النتائج المحلية
    if (localResults) {
      localResults.forEach((error: any) => {
        const key = `${error.startIndex}-${error.endIndex}`;
        combinedErrors.set(key, {
          ...error,
          source: "local",
          confidence: 0.7
        });
      });
    }

    // إضافة أو تحديث النتائج من Microsoft
    if (microsoftResults) {
      microsoftResults.forEach((error: any) => {
        const key = `${error.startIndex}-${error.endIndex}`;
        const existing = combinedErrors.get(key);
        
        if (existing) {
          // دمج الاقتراحات وإزالة التكرارات
          const allSuggestions = [...existing.suggestions, ...error.suggestions];
          const uniqueSuggestions = Array.from(new Set(allSuggestions));
          
          combinedErrors.set(key, {
            ...existing,
            suggestions: uniqueSuggestions.slice(0, 5),
            confidence: Math.max(existing.confidence, error.confidence),
            source: "combined"
          });
        } else {
          combinedErrors.set(key, {
            ...error,
            source: "microsoft"
          });
        }
      });
    }

    // تحويل إلى مصفوفة وترتيب حسب الموقع
    const finalResults = Array.from(combinedErrors.values())
      .sort((a: any, b: any) => a.startIndex - b.startIndex);

    return finalResults;
  },
});
