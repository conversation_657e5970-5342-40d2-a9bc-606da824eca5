import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import Spell<PERSON>he<PERSON> from "./SpellChecker";
import AITextEditor from "./AITextEditor";
import WelcomePage from "./WelcomePage";
import DarkModeToggle from "./components/DarkModeToggle";
import { useState } from "react";

export default function App() {
  const [showWelcome, setShowWelcome] = useState(true);
  const [activeTab, setActiveTab] = useState<'spellcheck' | 'editor'>('editor');

  // إذا كان المستخدم يريد رؤية صفحة الترحيب
  if (showWelcome) {
    return <WelcomePage onGetStarted={() => setShowWelcome(false)} />;
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900" dir="rtl">
      <header className="sticky top-0 z-10 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 h-16 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setShowWelcome(true)}
              className="text-xl font-bold text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 transition-colors"
            >
              محرر النصوص الذكي
            </button>
            <Authenticated>
              <nav className="flex gap-2">
                <button
                  onClick={() => setActiveTab('editor')}
                  className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                    activeTab === 'editor'
                      ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 font-medium'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800'
                  }`}
                >
                  المحرر الذكي
                </button>
                <button
                  onClick={() => setActiveTab('spellcheck')}
                  className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                    activeTab === 'spellcheck'
                      ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 font-medium'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800'
                  }`}
                >
                  مصحح الإملاء
                </button>
              </nav>
            </Authenticated>
          </div>
          <div className="flex items-center gap-3">
            <DarkModeToggle />
            <Authenticated>
              <SignOutButton />
            </Authenticated>
          </div>
        </div>
      </header>
      
      <main className="flex-1 p-4">
        <Content activeTab={activeTab} />
      </main>
      
      <Toaster position="top-center" />
    </div>
  );
}

function Content({ activeTab }: { activeTab: 'spellcheck' | 'editor' }) {
  const loggedInUser = useQuery(api.auth.loggedInUser);

  if (loggedInUser === undefined) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 dark:border-indigo-400 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <Authenticated>
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 dark:text-gray-100 mb-4">
            {activeTab === 'editor' ? 'المحرر الذكي للنصوص العربية' : 'مصحح الإملاء العربي'}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            أهلاً {loggedInUser?.email ?? "صديقي"}!
            {activeTab === 'editor'
              ? ' استخدم الذكاء الاصطناعي لتحرير وتحسين نصوصك'
              : ' اكتب نصك وسنساعدك في تصحيح الأخطاء الإملائية'
            }
          </p>
        </div>

        {activeTab === 'editor' ? <AITextEditor /> : <SpellChecker />}
      </Authenticated>
      
      <Unauthenticated>
        <div className="max-w-md mx-auto mt-20">
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </div>
            <h1 className="text-4xl font-bold text-gray-800 dark:text-gray-100 mb-4">
              محرر النصوص الذكي
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              سجل دخولك للبدء في تحرير وتصحيح النصوص العربية باستخدام الذكاء الاصطناعي
            </p>
          </div>
          <SignInForm />
        </div>
      </Unauthenticated>
    </div>
  );
}
