import React, { useState, useRef, useCallback } from 'react';
import { toast } from 'sonner';
import { useAuth } from './hooks/useAuth';
import { saveText, getUserTexts, SavedText } from './firebase/firestore';

interface Analysis {
  grammarErrors: number;
  styleImprovements: number;
  readabilityScore: number;
  notes?: string;
}

interface CorrectionResult {
  correctedText: string;
  analysis: Analysis;
  suggestions: string[];
}

export default function AITextEditor() {
  const [originalText, setOriginalText] = useState('');
  const [correctedText, setCorrectedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [correctionType, setCorrectionType] = useState<'grammar' | 'style' | 'comprehensive'>('comprehensive');
  const [writingContext, setWritingContext] = useState<'novel' | 'article' | 'essay' | 'letter' | 'general'>('general');
  const [analysis, setAnalysis] = useState<Analysis | null>(null);
  const [writingSuggestions, setWritingSuggestions] = useState<string[]>([]);
  const [showAnalysis, setShowAnalysis] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { user } = useAuth();

  const handleCorrection = useCallback(async () => {
    if (!originalText.trim()) {
      toast.error('يرجى إدخال نص للتحليل');
      return;
    }

    setIsProcessing(true);
    try {
      // محاكاة تحليل وتصحيح النص
      const result = await simulateTextCorrection(originalText, correctionType);

      setCorrectedText(result.correctedText);
      setAnalysis(result.analysis);
      setShowAnalysis(true);

      toast.success('تم تحليل وتصحيح النص بنجاح');
    } catch (error) {
      console.error('Error correcting text:', error);
      toast.error('حدث خطأ أثناء تحليل النص');
    } finally {
      setIsProcessing(false);
    }
  }, [originalText, correctionType]);

  const handleGenerateSuggestions = useCallback(async () => {
    if (!originalText.trim()) {
      toast.error('يرجى إدخال نص للحصول على اقتراحات');
      return;
    }

    setIsProcessing(true);
    try {
      const suggestions = await simulateWritingSuggestions(originalText, writingContext);

      setWritingSuggestions(suggestions);
      setShowSuggestions(true);

      toast.success('تم توليد الاقتراحات بنجاح');
    } catch (error) {
      console.error('Error generating suggestions:', error);
      toast.error('حدث خطأ أثناء توليد الاقتراحات');
    } finally {
      setIsProcessing(false);
    }
  }, [originalText, writingContext]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('تم نسخ النص');
  };

  const applyCorrection = () => {
    setOriginalText(correctedText);
    setCorrectedText('');
    setAnalysis(null);
    setShowAnalysis(false);
    toast.success('تم تطبيق التصحيح');
  };

  const clearAll = () => {
    setOriginalText('');
    setCorrectedText('');
    setAnalysis(null);
    setWritingSuggestions([]);
    setShowAnalysis(false);
    setShowSuggestions(false);
  };

  // محاكاة تصحيح النص
  const simulateTextCorrection = async (text: string, type: string): Promise<CorrectionResult> => {
    // تأخير لمحاكاة المعالجة
    await new Promise(resolve => setTimeout(resolve, 2000));

    const wordCount = text.trim().split(/\s+/).length;
    const grammarErrors = Math.floor(wordCount * 0.1); // 10% من الكلمات
    const styleImprovements = Math.floor(wordCount * 0.15); // 15% من الكلمات

    return {
      correctedText: text + '\n\n[تم تحسين النص وتصحيح الأخطاء النحوية والإملائية]',
      analysis: {
        grammarErrors,
        styleImprovements,
        readabilityScore: Math.floor(Math.random() * 30) + 70, // بين 70-100
        notes: `تم تحليل النص وإجراء ${grammarErrors} تصحيح نحوي و ${styleImprovements} تحسين أسلوبي.`
      }
    };
  };

  // محاكاة اقتراحات الكتابة
  const simulateWritingSuggestions = async (text: string, context: string): Promise<string[]> => {
    await new Promise(resolve => setTimeout(resolve, 1500));

    const suggestions = [
      'استخدم جملاً أقصر لتحسين وضوح النص',
      'أضف المزيد من الأمثلة لتوضيح النقاط المهمة',
      'استخدم كلمات انتقالية لربط الأفكار',
      'تجنب التكرار غير الضروري للكلمات',
      'استخدم الصوت النشط بدلاً من المبني للمجهول'
    ];

    return suggestions.slice(0, Math.floor(Math.random() * 3) + 2); // 2-4 اقتراحات
  };

  return (
    <div className="space-y-6">
      {/* شريط الأدوات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
        <div className="flex flex-wrap gap-4 items-center justify-between mb-4">
          <div className="flex flex-wrap gap-3">
            <select
              value={correctionType}
              onChange={(e) => setCorrectionType(e.target.value as any)}
              className="px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="comprehensive">تصحيح شامل</option>
              <option value="grammar">تصحيح نحوي</option>
              <option value="style">تحسين الأسلوب</option>
            </select>

            <select
              value={writingContext}
              onChange={(e) => setWritingContext(e.target.value as any)}
              className="px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="general">كتابة عامة</option>
              <option value="novel">رواية</option>
              <option value="article">مقال</option>
              <option value="essay">مقال أكاديمي</option>
              <option value="letter">رسالة رسمية</option>
            </select>
          </div>

          <div className="flex gap-2">
            <button
              onClick={handleCorrection}
              disabled={isProcessing || !originalText.trim()}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري التحليل...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  تحليل وتصحيح
                </>
              )}
            </button>

            <button
              onClick={handleGenerateSuggestions}
              disabled={isProcessing || !originalText.trim()}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              اقتراحات الكتابة
            </button>

            <button
              onClick={clearAll}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-200"
            >
              مسح الكل
            </button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-300">
          <span>الكلمات: {originalText.trim().split(/\s+/).filter(w => w).length}</span>
          <span>الأحرف: {originalText.length}</span>
          <span>الأسطر: {originalText.split('\n').length}</span>
        </div>
      </div>

      {/* منطقة الكتابة والتحرير */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* النص الأصلي */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900 dark:to-indigo-900 px-6 py-4 border-b border-gray-100 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
              <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
              مساحة الكتابة
            </h3>
          </div>
          <div className="p-6">
            <textarea
              ref={textareaRef}
              value={originalText}
              onChange={(e) => setOriginalText(e.target.value)}
              placeholder="ابدأ بكتابة نصك هنا... يمكنك كتابة رواية، مقال، رسالة، أو أي نص تريد تحسينه وتصحيحه."
              className="w-full h-96 p-4 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-lg leading-relaxed font-arabic bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              dir="rtl"
              style={{ fontFamily: 'Noto Sans Arabic, sans-serif' }}
            />
          </div>
        </div>

        {/* النص المحرر */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900 dark:to-emerald-900 px-6 py-4 border-b border-gray-100 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
                <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                النص المحرر
              </h3>
              {correctedText && (
                <div className="flex gap-2">
                  <button
                    onClick={() => copyToClipboard(correctedText)}
                    className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors text-sm"
                  >
                    نسخ
                  </button>
                  <button
                    onClick={applyCorrection}
                    className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-md hover:bg-green-200 dark:hover:bg-green-800 transition-colors text-sm"
                  >
                    تطبيق
                  </button>
                </div>
              )}
            </div>
          </div>
          <div className="p-6">
            <div className="w-full h-96 p-4 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg overflow-y-auto text-lg leading-relaxed font-arabic">
              {correctedText ? (
                <div dir="rtl" style={{ fontFamily: 'Noto Sans Arabic, sans-serif' }} className="text-gray-900 dark:text-gray-100">
                  {correctedText}
                </div>
              ) : (
                <div className="text-gray-500 dark:text-gray-400 text-center flex items-center justify-center h-full">
                  <div className="text-center">
                    <svg className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p>سيظهر النص المحرر هنا بعد التحليل</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* تحليل النص */}
      {showAnalysis && analysis && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
              <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              تحليل النص
            </h3>
            <button
              onClick={() => setShowAnalysis(false)}
              className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-red-50 dark:bg-red-900 p-4 rounded-lg border border-red-100 dark:border-red-800">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">{analysis.grammarErrors}</div>
              <div className="text-sm text-red-700 dark:text-red-300">أخطاء نحوية</div>
            </div>
            <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg border border-blue-100 dark:border-blue-800">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{analysis.styleImprovements}</div>
              <div className="text-sm text-blue-700 dark:text-blue-300">تحسينات أسلوبية</div>
            </div>
            <div className="bg-green-50 dark:bg-green-900 p-4 rounded-lg border border-green-100 dark:border-green-800">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">{analysis.readabilityScore}%</div>
              <div className="text-sm text-green-700 dark:text-green-300">مستوى القراءة</div>
            </div>
          </div>

          {analysis.notes && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
              <h4 className="font-medium text-gray-800 dark:text-gray-100 mb-2">ملاحظات التحليل:</h4>
              <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">{analysis.notes}</p>
            </div>
          )}
        </div>
      )}

      {/* اقتراحات الكتابة */}
      {showSuggestions && writingSuggestions.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
              <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              اقتراحات لتحسين الكتابة
            </h3>
            <button
              onClick={() => setShowSuggestions(false)}
              className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="space-y-3">
            {writingSuggestions.map((suggestion, index) => (
              <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-100 dark:border-yellow-800 rounded-lg">
                <div className="w-6 h-6 bg-yellow-200 dark:bg-yellow-700 text-yellow-800 dark:text-yellow-200 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                  {index + 1}
                </div>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{suggestion}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
