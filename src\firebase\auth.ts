import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut, 
  sendPasswordResetEmail,
  updateProfile,
  User,
  AuthError
} from "firebase/auth";
import { auth } from "./config";

export interface AuthResult {
  success: boolean;
  user?: User;
  error?: string;
}

// تسجيل مستخدم جديد
export const signUpWithEmail = async (
  email: string, 
  password: string, 
  displayName?: string
): Promise<AuthResult> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    
    // تحديث اسم المستخدم إذا تم توفيره
    if (displayName && userCredential.user) {
      await updateProfile(userCredential.user, { displayName });
    }
    
    return {
      success: true,
      user: userCredential.user
    };
  } catch (error) {
    const authError = error as AuthError;
    return {
      success: false,
      error: getErrorMessage(authError.code)
    };
  }
};

// تسجيل الدخول
export const signInWithEmail = async (
  email: string, 
  password: string
): Promise<AuthResult> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return {
      success: true,
      user: userCredential.user
    };
  } catch (error) {
    const authError = error as AuthError;
    return {
      success: false,
      error: getErrorMessage(authError.code)
    };
  }
};

// تسجيل الخروج
export const signOutUser = async (): Promise<AuthResult> => {
  try {
    await signOut(auth);
    return { success: true };
  } catch (error) {
    const authError = error as AuthError;
    return {
      success: false,
      error: getErrorMessage(authError.code)
    };
  }
};

// إعادة تعيين كلمة المرور
export const resetPassword = async (email: string): Promise<AuthResult> => {
  try {
    // إعدادات إضافية لإعادة تعيين كلمة المرور
    const actionCodeSettings = {
      url: 'https://toika-369.web.app/login', // رابط العودة بعد إعادة التعيين
      handleCodeInApp: false, // سيتم التعامل مع الكود في المتصفح وليس في التطبيق
    };

    await sendPasswordResetEmail(auth, email, actionCodeSettings);

    console.log('Password reset email sent successfully to:', email);

    return {
      success: true,
      error: `تم إرسال رابط إعادة تعيين كلمة المرور إلى ${email}. يرجى التحقق من صندوق الوارد وصندوق الرسائل غير المرغوب فيها.`
    };
  } catch (error) {
    const authError = error as AuthError;
    console.error('Password reset error:', authError);

    return {
      success: false,
      error: getErrorMessage(authError.code)
    };
  }
};

// ترجمة رسائل الخطأ إلى العربية
const getErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'لا يوجد حساب مسجل بهذا البريد الإلكتروني. يرجى التأكد من البريد الإلكتروني أو إنشاء حساب جديد.';
    case 'auth/wrong-password':
      return 'كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى أو استخدام "نسيت كلمة المرور".';
    case 'auth/email-already-in-use':
      return 'هذا البريد الإلكتروني مستخدم بالفعل. يرجى تسجيل الدخول أو استخدام بريد إلكتروني آخر.';
    case 'auth/weak-password':
      return 'كلمة المرور ضعيفة جداً. يجب أن تحتوي على 6 أحرف على الأقل.';
    case 'auth/invalid-email':
      return 'البريد الإلكتروني غير صحيح. يرجى التأكد من صيغة البريد الإلكتروني.';
    case 'auth/too-many-requests':
      return 'تم إرسال عدد كبير من الطلبات. يرجى الانتظار قليلاً ثم المحاولة مرة أخرى.';
    case 'auth/network-request-failed':
      return 'خطأ في الاتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';
    case 'auth/invalid-action-code':
      return 'رابط إعادة تعيين كلمة المرور غير صحيح أو منتهي الصلاحية.';
    case 'auth/expired-action-code':
      return 'رابط إعادة تعيين كلمة المرور منتهي الصلاحية. يرجى طلب رابط جديد.';
    case 'auth/missing-email':
      return 'يرجى إدخال البريد الإلكتروني.';
    case 'auth/operation-not-allowed':
      return 'تسجيل الدخول بالبريد الإلكتروني غير مفعل. يرجى التواصل مع الدعم الفني.';
    default:
      return `حدث خطأ غير متوقع (${errorCode}). يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.`;
  }
};
