import React, { useState, useEffect, useRef, useCallback } from 'react';
import { toast } from 'sonner';
import { useAuth } from './hooks/useAuth';
import { saveText, getUserTexts, deleteText, SavedText } from './firebase/firestore';

interface SpellingError {
  word: string;
  position: number;
  suggestions: string[];
  startIndex: number;
  endIndex: number;
  confidence?: number;
  source?: string;
  type?: string;
}

export default function SpellChecker() {
  const [originalText, setOriginalText] = useState('');
  const [correctedText, setCorrectedText] = useState('');
  const [selectedError, setSelectedError] = useState<SpellingError | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [title, setTitle] = useState('');
  const [showSavedTexts, setShowSavedTexts] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [useEnhancedCheck, setUseEnhancedCheck] = useState(true);
  const [autoCorrect, setAutoCorrect] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const checkTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const [spellingErrors, setSpellingErrors] = useState<SpellingError[]>([]);

  // جلب النصوص المحفوظة عند تحميل المكون
  useEffect(() => {
    if (user?.uid) {
      loadUserTexts();
    }
  }, [user]);

  const loadUserTexts = async () => {
    if (user?.uid) {
      const texts = await getUserTexts(user.uid);
      setSavedTexts(texts);
    }
  };

  // التحقق من الإملاء (محلي أو محسن)
  const performSpellCheck = useCallback(async (text: string) => {
    if (!text.trim()) {
      setSpellingErrors([]);
      return;
    }

    setIsChecking(true);
    try {
      let errors: SpellingError[] = [];

      if (useEnhancedCheck) {
        // استخدام Microsoft API (يمكن إضافته لاحقاً)
        // في الوقت الحالي سنستخدم التحقق المحلي
        errors = await performLocalSpellCheck(text);
      } else {
        errors = await performLocalSpellCheck(text);
      }

      setSpellingErrors(errors || []);

      // التصحيح التلقائي إذا كان مفعلاً
      if (autoCorrect && errors && errors.length > 0) {
        let corrected = text;
        const sortedErrors = [...errors].sort((a, b) => b.startIndex - a.startIndex);

        sortedErrors.forEach(error => {
          if (error.suggestions.length > 0) {
            corrected = corrected.substring(0, error.startIndex) +
                      error.suggestions[0] +
                      corrected.substring(error.endIndex);
          }
        });

        if (corrected !== text) {
          setCorrectedText(corrected);
          toast.success(`تم تصحيح ${sortedErrors.length} كلمة تلقائياً`);
        }
      }
    } catch (error) {
      console.error('Error in spell check:', error);
      toast.error('حدث خطأ أثناء التحقق من الإملاء');
    } finally {
      setIsChecking(false);
    }
  }, [useEnhancedCheck, autoCorrect]);

  // التحقق المحلي من الإملاء
  const performLocalSpellCheck = async (text: string): Promise<SpellingError[]> => {
    // قائمة بسيطة من الأخطاء الشائعة للتجربة
    const commonErrors = [
      { wrong: 'اللة', correct: 'الله' },
      { wrong: 'انشاء', correct: 'إنشاء' },
      { wrong: 'الى', correct: 'إلى' },
      { wrong: 'هاذا', correct: 'هذا' },
      { wrong: 'هاذه', correct: 'هذه' },
    ];

    const errors: SpellingError[] = [];
    const words = text.split(/\s+/);
    let currentIndex = 0;

    words.forEach(word => {
      const cleanWord = word.replace(/[^\u0600-\u06FF]/g, '');
      const error = commonErrors.find(e => e.wrong === cleanWord);

      if (error) {
        const startIndex = text.indexOf(word, currentIndex);
        errors.push({
          word: cleanWord,
          startIndex,
          endIndex: startIndex + word.length,
          suggestions: [error.correct],
          source: 'local'
        });
      }

      currentIndex = text.indexOf(word, currentIndex) + word.length;
    });

    return errors;
  };

  // تأخير التحقق لتحسين الأداء
  useEffect(() => {
    if (originalText.trim()) {
      if (checkTimeoutRef.current) {
        clearTimeout(checkTimeoutRef.current);
      }

      checkTimeoutRef.current = setTimeout(() => {
        performSpellCheck(originalText);
      }, 1000); // تأخير ثانية واحدة
    }

    return () => {
      if (checkTimeoutRef.current) {
        clearTimeout(checkTimeoutRef.current);
      }
    };
  }, [originalText, performSpellCheck]);

  useEffect(() => {
    setCorrectedText(originalText);
  }, [originalText]);

  const handleWordClick = (error: SpellingError) => {
    setSelectedError(error);
    setShowSuggestions(true);
  };

  const applySuggestion = (suggestion: string) => {
    if (!selectedError) return;
    
    const newText = correctedText.substring(0, selectedError.startIndex) + 
                   suggestion + 
                   correctedText.substring(selectedError.endIndex);
    
    setCorrectedText(newText);
    setShowSuggestions(false);
    setSelectedError(null);
    toast.success('تم تطبيق التصحيح');
  };

  const applyAllSuggestions = () => {
    if (spellingErrors.length === 0) return;
    
    let corrected = correctedText;
    const sortedErrors = [...spellingErrors].sort((a, b) => b.startIndex - a.startIndex);
    let appliedCount = 0;
    
    sortedErrors.forEach(error => {
      if (error.suggestions.length > 0) {
        corrected = corrected.substring(0, error.startIndex) + 
                   error.suggestions[0] + 
                   corrected.substring(error.endIndex);
        appliedCount++;
      }
    });
    
    setCorrectedText(corrected);
    toast.success(`تم تطبيق ${appliedCount} تصحيح`);
  };

  const handleSave = async () => {
    if (!originalText.trim()) {
      toast.error('يرجى إدخال نص للحفظ');
      return;
    }

    if (!user?.uid) {
      toast.error('يجب تسجيل الدخول أولاً');
      return;
    }

    try {
      const result = await saveText(
        title || `نص ${new Date().toLocaleDateString('ar-SA')}`,
        originalText,
        correctedText,
        user.uid,
        'spellcheck'
      );

      if (result.success) {
        toast.success('تم حفظ النص بنجاح');
        setTitle('');
        loadUserTexts(); // إعادة تحميل النصوص
      } else {
        toast.error(result.error || 'حدث خطأ أثناء حفظ النص');
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ النص');
    }
  };

  const handleDelete = async (textId: string) => {
    try {
      const result = await deleteText(textId);
      if (result.success) {
        toast.success('تم حذف النص بنجاح');
        loadUserTexts(); // إعادة تحميل النصوص
      } else {
        toast.error(result.error || 'حدث خطأ أثناء حذف النص');
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء حذف النص');
    }
  };

  const loadSavedText = (text: SavedText) => {
    setOriginalText(text.originalText);
    setCorrectedText(text.correctedText || text.originalText);
    setTitle(text.title || '');
    setShowSavedTexts(false);
    toast.success('تم تحميل النص');
  };

  const renderTextWithErrors = (text: string, errors: SpellingError[]) => {
    if (!errors || errors.length === 0) {
      return <span>{text}</span>;
    }

    const parts = [];
    let lastIndex = 0;

    errors.forEach((error, index) => {
      if (error.startIndex > lastIndex) {
        parts.push(
          <span key={`text-${index}`}>
            {text.substring(lastIndex, error.startIndex)}
          </span>
        );
      }

      const errorClass = `
        cursor-pointer rounded px-1 border-b-2 transition-all duration-200
        ${error.source === 'microsoft' ? 'bg-blue-200 text-blue-800 border-blue-400 hover:bg-blue-300' : 
          error.source === 'combined' ? 'bg-purple-200 text-purple-800 border-purple-400 hover:bg-purple-300' :
          'bg-red-200 text-red-800 border-red-400 hover:bg-red-300'}
      `;

      parts.push(
        <span
          key={`error-${index}`}
          className={errorClass}
          onClick={() => handleWordClick(error)}
          title={`${error.source === 'microsoft' ? 'Microsoft' : error.source === 'combined' ? 'مدمج' : 'محلي'} - اقتراحات: ${error.suggestions.join(', ')}`}
        >
          {text.substring(error.startIndex, error.endIndex)}
        </span>
      );

      lastIndex = error.endIndex;
    });

    if (lastIndex < text.length) {
      parts.push(
        <span key="text-end">
          {text.substring(lastIndex)}
        </span>
      );
    }

    return <>{parts}</>;
  };

  return (
    <div className="space-y-6">
      {/* شريط الأدوات المحسن */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-100 dark:border-gray-700">
        <div className="flex flex-wrap gap-4 items-center justify-between mb-4">
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setShowSavedTexts(!showSavedTexts)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              النصوص المحفوظة
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              حفظ النص
            </button>
            {spellingErrors.length > 0 && (
              <button
                onClick={applyAllSuggestions}
                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                تطبيق جميع التصحيحات
              </button>
            )}
          </div>

          <div className="flex items-center gap-2">
            <input
              type="text"
              placeholder="عنوان النص (اختياري)"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
        </div>

        {/* إعدادات التحقق */}
        <div className="flex flex-wrap gap-4 items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={useEnhancedCheck}
              onChange={(e) => setUseEnhancedCheck(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">استخدام التحقق المحسن (Microsoft)</span>
          </label>

          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={autoCorrect}
              onChange={(e) => setAutoCorrect(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">التصحيح التلقائي</span>
          </label>

          {isChecking && (
            <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 dark:border-blue-400"></div>
              <span className="text-sm">جاري التحقق...</span>
            </div>
          )}
        </div>
      </div>

      {/* النصوص المحفوظة */}
      {showSavedTexts && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-100 dark:border-gray-700">
          <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-100">النصوص المحفوظة</h3>
          {savedTexts && savedTexts.length > 0 ? (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {savedTexts.map((text) => (
                <div key={text.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-800 dark:text-gray-100">{text.title}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
                      {text.originalText.substring(0, 100)}...
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500">
                      {text.createdAt.toDate().toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => loadSavedText(text)}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      تحميل
                    </button>
                    <button
                      onClick={() => handleDelete(text.id)}
                      className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                    >
                      حذف
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 dark:text-gray-400 text-center py-4">لا توجد نصوص محفوظة</p>
          )}
        </div>
      )}

      {/* منطقة الكتابة والتصحيح */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* النص الأصلي */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-100 dark:border-gray-700">
          <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-100">النص الأصلي</h3>
          <textarea
            ref={textareaRef}
            value={originalText}
            onChange={(e) => setOriginalText(e.target.value)}
            placeholder="اكتب النص العربي هنا للتحقق من الإملاء..."
            className="w-full h-80 p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-lg leading-relaxed bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            dir="rtl"
          />

          {spellingErrors.length > 0 && (
            <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                تم العثور على {spellingErrors.length} خطأ إملائي.
                {useEnhancedCheck && (
                  <span className="block mt-1">
                    🔵 أزرق: Microsoft | 🟣 بنفسجي: مدمج | 🔴 أحمر: محلي
                  </span>
                )}
              </p>
            </div>
          )}
        </div>

        {/* النص المصحح */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-100 dark:border-gray-700">
          <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-100">النص المصحح</h3>
          <div className="w-full h-80 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 overflow-y-auto text-lg leading-relaxed">
            {correctedText ? (
              renderTextWithErrors(correctedText, spellingErrors)
            ) : (
              <span className="text-gray-500 dark:text-gray-400">سيظهر النص المصحح هنا...</span>
            )}
          </div>

          {spellingErrors.length === 0 && originalText.trim() && !isChecking && (
            <div className="mt-4 p-3 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg">
              <p className="text-sm text-green-800 dark:text-green-200">
                ممتاز! لم يتم العثور على أخطاء إملائية في النص.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* نافذة الاقتراحات المحسنة */}
      {showSuggestions && selectedError && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" dir="rtl">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md w-full mx-4 border border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">اقتراحات للكلمة: "{selectedError.word}"</h3>
              {selectedError.source && (
                <span className={`px-2 py-1 rounded text-xs ${
                  selectedError.source === 'microsoft' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :
                  selectedError.source === 'combined' ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' :
                  'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                }`}>
                  {selectedError.source === 'microsoft' ? 'Microsoft' :
                   selectedError.source === 'combined' ? 'مدمج' : 'محلي'}
                </span>
              )}
            </div>

            <div className="space-y-2 mb-6">
              {selectedError.suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => applySuggestion(suggestion)}
                  className="w-full p-3 text-right bg-gray-50 dark:bg-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900 border border-gray-200 dark:border-gray-600 rounded-lg transition-colors text-gray-800 dark:text-gray-100"
                >
                  {suggestion}
                </button>
              ))}

              {selectedError.suggestions.length === 0 && (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                  لا توجد اقتراحات متاحة لهذه الكلمة
                </p>
              )}
            </div>

            <div className="flex gap-2">
              <button
                onClick={() => {
                  setShowSuggestions(false);
                  setSelectedError(null);
                }}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                إغلاق
              </button>
              <button
                onClick={() => applySuggestion(selectedError.word)}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                إبقاء كما هو
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
