// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyCDfsVltjGVbGilr__evf1tsyMA8811Oqg",
  authDomain: "toika-369.firebaseapp.com",
  projectId: "toika-369",
  storageBucket: "toika-369.firebasestorage.app",
  messagingSenderId: "300804286264",
  appId: "1:300804286264:web:878241255e9a623d634bcc",
  measurementId: "G-0L5FJN68H2"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Initialize Analytics only in browser environment
export const analytics = typeof window !== 'undefined' ? getAnalytics(app) : null;

export default app;
