// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyCDfsVltjGVbGilr__evf1tsyMA8811Oqg",
  authDomain: "test-web-7a63d.firebaseapp.com",
  projectId: "test-web-7a63d",
  storageBucket: "test-web-7a63d.firebasestorage.app",
  messagingSenderId: "183179158565",
  appId: "1:183179158565:web:178875e2283ebd94634bcc",
  measurementId: "G-YYYWZ8DS0W"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const analytics = getAnalytics(app);

export default app;
