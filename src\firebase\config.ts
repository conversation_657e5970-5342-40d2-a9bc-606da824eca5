// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyBXUdqtI7UmgUXFTmoZ0317OMxbZ4kBG4k",
  authDomain: "web-text-b4599.firebaseapp.com",
  projectId: "web-text-b4599",
  storageBucket: "web-text-b4599.firebasestorage.app",
  messagingSenderId: "867024240156",
  appId: "1:867024240156:web:98de4b0c4080aa6c1b69d9",
  measurementId: "G-Z6DJD0C5XH"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Initialize Analytics only in browser environment
export const analytics = typeof window !== 'undefined' ? getAnalytics(app) : null;

export default app;
