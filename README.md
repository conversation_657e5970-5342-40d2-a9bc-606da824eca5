# 📚 المدقّق العربي الذكي | Arabic AI Proofreader

🧠 **المدقّق العربي الذكي** هو موقع ويب عربي ذكي ومتقدم يعتمد على تقنيات الذكاء الاصطناعي لتحسين جودة النصوص العربية، من خلال تصحيح الأخطاء الإملائية والنحوية بدقة وسرعة عالية.  
يُسهل الموقع على المستخدمين — من طلاب، كتّاب، محررين، ومحترفين — إنتاج نصوص عربية صحيحة وخالية من الأخطاء، مع دعم لواجهة استخدام سهلة ومناسبة لجميع الأجهزة.

---

## 🚀 نبذة عن المشروع

تم تطوير "المدقّق العربي الذكي" ليكون الأداة المثالية لتصحيح النصوص العربية بجودة احترافية، حيث يقوم بـ:

- تدقيق وتصحيح الأخطاء الإملائية بشكل آلي وفوري  
- فحص النصوص من الناحية النحوية واقتراح تصحيحات ذكية  
- تقديم بدائل لغوية وتحسين تركيب الجمل  
- دعم كامل للتشكيل العربي وتحسينه  
- تقديم تجربة استخدام سلسة وبسيطة للجميع  

---

## ✨ الميزات الرئيسية

- ✅ **تصحيح إملائي ونحوي متقدم** بدقة عالية باستخدام تقنيات الذكاء الاصطناعي  
- ✅ **اقتراحات لغوية ذكية** تساعد المستخدم على تحسين جودة كتابته  
- ✅ دعم شامل لجميع النصوص العربية (فصحى، لهجات، تشكيل)  
- ✅ واجهة مستخدم **سهلة الاستخدام ومتجاوبة مع جميع الأجهزة** (جوال، تاب، كمبيوتر)  
- ✅ **نظام تسجيل دخول آمن** عبر Firebase Authentication  
- ✅ إمكانية **إعادة تعيين كلمة المرور** عبر روابط مخصصة ترسل إلى البريد الإلكتروني  
- ✅ حماية البيانات والتخزين عبر Firebase Firestore مع قواعد أمان متقدمة  
- ✅ **اتصال مشفر SSL** لضمان سرية وأمان البيانات أثناء التصفح  
- ✅ مراجعة وتحديث دوري لأنظمة الحماية ومنع محاولات التجاوز (overposting وغيرها)  

---

## 📥 كيفية الاستخدام

1. افتح الموقع وأدخل النص العربي الذي تريد تدقيقه في الحقل المخصص.  
2. اضغط على زر "**صحّح الآن**" لبدء عملية التدقيق.  
3. بعد بضع ثوانٍ، سيظهر النص المصحح مع التعديلات المقترحة.  
4. يمكنك نسخ النص المصحح أو تحميله بسهولة لمشاركته أو استخدامه لاحقًا.  
5. في حالة وجود حساب، يمكنك حفظ النصوص المراجعة في حسابك للوصول إليها في أي وقت.

---

## 🔐 الأمان والخصوصية

- يعتمد الموقع على **Firebase Authentication** لضمان تسجيل دخول آمن وسلس.  
- تخزين النصوص والبيانات يتم عبر **Firebase Firestore** مع قواعد حماية صارمة لضمان خصوصية المستخدمين.  
- الموقع يعمل عبر **اتصال مشفر (HTTPS / SSL)** لضمان سرية البيانات أثناء النقل.  
- نظام الحماية مصمم لمنع محاولات الاختراق والهجمات مثل **overposting**، مما يحافظ على سلامة البيانات والمستخدمين.  
- تحديثات دورية للأنظمة الأمنية لضمان أعلى معايير الحماية.  

---

## 🔍 كلمات مفتاحية (Keywords) لمحركات البحث


---

## 🛠️ التقنيات المستخدمة

- **HTML5, CSS3, JavaScript (ES6+)** لإنشاء واجهة مستخدم تفاعلية ومتجاوبة  
- **Firebase Authentication** لنظام تسجيل دخول آمن  
- **Firebase Firestore** لتخزين وحفظ النصوص بشكل آمن  
- **Firebase Hosting** لنشر الموقع بسرعة وبكفاءة عالية  
- **تقنيات الذكاء الاصطناعي NLP** مخصصة للغة العربية لتحليل وتصحيح النصوص  
- **اتصال مشفر (HTTPS/SSL)** لضمان أمان البيانات  

---

## 🚀 خطوات النشر على Firebase Hosting

### المتطلبات الأساسية:
1. حساب Firebase
2. Node.js مثبت على الجهاز
3. Firebase CLI

### خطوات الإعداد في Firebase Console:

#### 1. تفعيل Firebase Hosting:
- اذهب إلى [Firebase Console](https://console.firebase.google.com/)
- اختر مشروع `test-web-7a63d`
- من القائمة الجانبية، اختر **"Hosting"**
- اضغط **"Get started"**
- اتبع الخطوات لتفعيل Hosting

#### 2. تفعيل Authentication:
- في نفس المشروع، اختر **"Authentication"**
- اذهب إلى تبويب **"Sign-in method"**
- فعّل **"Email/Password"**
- احفظ التغييرات

#### 3. تفعيل Firestore Database:
- اختر **"Firestore Database"**
- اضغط **"Create database"**
- اختر **"Start in test mode"** (مؤقتاً)
- اختر المنطقة الجغرافية المناسبة

### خطوات النشر:

```bash
# 1. تثبيت Firebase CLI
npm install -g firebase-tools

# 2. تسجيل الدخول إلى Firebase
firebase login

# 3. بناء المشروع
npm run build

# 4. النشر
firebase deploy --project test-web-7a63d
```

### إعدادات إضافية:

#### قواعد Firestore Security:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /savedTexts/{document} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
  }
}
```

---

## 🌐 رابط الموقع الرسمي

[اضغط هنا لزيارة الموقع](https://test-web-7a63d.web.app/)

---

## 👤 المطوّر

**بختيار** – مطوّر واجهات ومبرمج محترف  
🎯 إشراف ومساعدة فنية من: **Alan - Ala Studio**

📍 العراق – كلار / بان أسياو  
📧 للتواصل: [<EMAIL>](mailto:<EMAIL>)  

---

## ❤️ شكر وامتنان

نشكر جميع مستخدمي "المدقّق العربي الذكي" على ثقتهم، ونسعى دومًا لتطوير وتحسين الخدمة لتلبية حاجاتكم بأعلى جودة واحترافية.  

---

