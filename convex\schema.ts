import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  texts: defineTable({
    userId: v.id("users"),
    originalText: v.string(),
    correctedText: v.string(),
    title: v.optional(v.string()),
  }).index("by_user", ["userId"]),
  
  corrections: defineTable({
    textId: v.id("texts"),
    originalWord: v.string(),
    correctedWord: v.string(),
    position: v.number(),
    accepted: v.boolean(),
  }).index("by_text", ["textId"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
