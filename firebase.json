{"hosting": {"public": "dist", "site": "fix-template", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(woff|woff2|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}}