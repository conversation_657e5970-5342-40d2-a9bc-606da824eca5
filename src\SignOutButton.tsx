import { signOutUser } from "./firebase/auth";
import { toast } from "sonner";

export function SignOutButton() {
  const handleSignOut = async () => {
    const result = await signOutUser();
    if (result.success) {
      toast.success("تم تسجيل الخروج بنجاح");
    } else {
      toast.error("حدث خطأ أثناء تسجيل الخروج");
    }
  };

  return (
    <button
      className="px-4 py-2 rounded bg-white dark:bg-gray-800 text-secondary dark:text-gray-300 border border-gray-200 dark:border-gray-600 font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-secondary-hover dark:hover:text-gray-100 transition-colors shadow-sm hover:shadow"
      onClick={handleSignOut}
    >
      تسجيل الخروج
    </button>
  );
}
