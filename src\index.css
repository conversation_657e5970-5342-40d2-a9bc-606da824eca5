@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-light: #ffffff;
  --color-dark: #171717;
}

.accent-text {
  @apply text-slate-600;
}

/* Enhanced Arabic font support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap');

body {
  font-family: 'Noto Sans Arabic', 'Amiri', ui-sans-serif, system-ui, sans-serif;
  color: var(--color-dark);
  background: var(--color-light);
  line-height: 1.7;
}

/* Enhanced RTL support */
[dir="rtl"] {
  text-align: right;
}

/* Custom font classes */
.font-arabic {
  font-family: 'Noto Sans Arabic', sans-serif;
}

.font-arabic-serif {
  font-family: 'Amiri', serif;
}

/* Enhanced scrollbar for text areas */
.arabic-scroll::-webkit-scrollbar {
  width: 8px;
}

.arabic-scroll::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
}

.arabic-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.arabic-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced spell check highlighting */
.spell-error {
  cursor: pointer;
  padding: 1px 2px;
  border-radius: 3px;
  transition: all 0.2s ease;
  position: relative;
}

.spell-error-local {
  background-color: #fecaca;
  color: #dc2626;
  border-bottom: 2px wavy #dc2626;
}

.spell-error-microsoft {
  background-color: #dbeafe;
  color: #1d4ed8;
  border-bottom: 2px wavy #1d4ed8;
}

.spell-error-combined {
  background-color: #e9d5ff;
  color: #7c3aed;
  border-bottom: 2px wavy #7c3aed;
}

.spell-error:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.spell-error-local:hover {
  background-color: #fca5a5;
}

.spell-error-microsoft:hover {
  background-color: #bfdbfe;
}

.spell-error-combined:hover {
  background-color: #ddd6fe;
}

/* Enhanced button styles */
.btn-primary {
  @apply px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg;
}

.btn-secondary {
  @apply px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg hover:from-gray-700 hover:to-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg;
}

.btn-success {
  @apply px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg;
}

.btn-warning {
  @apply px-6 py-3 bg-gradient-to-r from-orange-600 to-amber-600 text-white rounded-lg hover:from-orange-700 hover:to-amber-700 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg;
}

.btn-danger {
  @apply px-6 py-3 bg-gradient-to-r from-red-600 to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-700 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg;
}

/* Enhanced input styles */
.input-enhanced {
  @apply w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white transition-all duration-200 shadow-sm hover:shadow-md;
}

.textarea-enhanced {
  @apply w-full p-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200 shadow-sm hover:shadow-md;
}

/* Enhanced card styles */
.card-enhanced {
  @apply bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200;
}

.card-gradient {
  @apply bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200;
}

/* Loading animations */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.loading-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

/* Enhanced focus styles for accessibility */
.focus-enhanced:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* Text selection styles */
::selection {
  background-color: #dbeafe;
  color: #1e40af;
}

::-moz-selection {
  background-color: #dbeafe;
  color: #1e40af;
}

/* Enhanced tooltip styles */
.tooltip-enhanced {
  position: relative;
}

.tooltip-enhanced::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1000;
}

.tooltip-enhanced:hover::before {
  opacity: 1;
}

/* Responsive text sizing */
@media (max-width: 768px) {
  .text-responsive {
    font-size: 0.875rem;
  }
  
  .btn-responsive {
    @apply px-4 py-2 text-sm;
  }
  
  .card-responsive {
    @apply p-4;
  }
}

@media (min-width: 769px) {
  .text-responsive {
    font-size: 1rem;
  }
  
  .card-responsive {
    @apply p-6;
  }
}

/* Enhanced animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(10px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

@keyframes slideDown {
  from { 
    transform: translateY(-10px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

/* Auth styles (unchanged) */
.auth-input-field {
  @apply w-full px-4 py-3 rounded-container bg-white border border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-shadow shadow-sm hover:shadow;
}

.auth-button {
  @apply w-full px-4 py-3 rounded bg-primary text-white font-semibold hover:bg-primary-hover transition-colors shadow-sm hover:shadow disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Enhanced gradient backgrounds */
.bg-gradient-soft {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-warm {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-gradient-cool {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Text editor specific styles */
.editor-container {
  @apply bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden;
}

.editor-header {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100;
}

.editor-content {
  @apply p-6;
}

.editor-textarea {
  @apply w-full h-96 p-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-lg leading-relaxed font-arabic;
}

.editor-output {
  @apply w-full h-96 p-4 bg-gray-50 border border-gray-200 rounded-lg overflow-y-auto text-lg leading-relaxed font-arabic;
}

/* Analysis cards */
.analysis-card {
  @apply p-4 rounded-lg border;
}

.analysis-card-error {
  @apply bg-red-50 border-red-100;
}

.analysis-card-improvement {
  @apply bg-blue-50 border-blue-100;
}

.analysis-card-score {
  @apply bg-green-50 border-green-100;
}

/* Suggestion items */
.suggestion-item {
  @apply flex items-start gap-3 p-3 bg-yellow-50 border border-yellow-100 rounded-lg;
}

.suggestion-number {
  @apply w-6 h-6 bg-yellow-200 text-yellow-800 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0;
}
