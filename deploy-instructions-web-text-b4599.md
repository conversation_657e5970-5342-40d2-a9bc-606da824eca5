# 🚀 تعليمات النشر على Firebase Hosting - web-text-b4599

## ✅ تم تحديث المشروع بنجاح!

### 🔧 الإعدادات المحدثة:
- **Project ID**: `web-text-b4599`
- **Domain**: `web-text-b4599.web.app`
- **Firebase Config**: تم تحديثه بالمعلومات الجديدة
- **Build**: تم بناء المشروع بنجاح في مجلد `dist/`

## 📋 خطوات النشر:

### الطريقة 1: Firebase CLI
```bash
# تسجيل الدخول (إذا لم تكن مسجل دخول)
firebase login

# تحديد المشروع
firebase use web-text-b4599

# النشر
firebase deploy --only hosting
```

### الطريقة 2: النشر اليدوي عبر Firebase Console
1. اذهب إلى: https://console.firebase.google.com/project/web-text-b4599/hosting
2. اضغط "Get started" إذا لم يكن Hosting مفعل
3. اسحب مجلد `dist` إلى منطقة الرفع
4. انتظر حتى ينتهي الرفع

## 🔥 إعداد Firebase Authentication:

### 1. تفعيل Authentication:
1. اذهب إلى: https://console.firebase.google.com/project/web-text-b4599/authentication
2. اضغط "Get started"
3. اذهب إلى "Sign-in method"
4. فعّل "Email/Password"

### 2. إعداد قالب البريد الإلكتروني:
1. اذهب إلى "Templates" → "Password reset"
2. عدّل الإعدادات:

**Sender name:**
```
المدقق العربي بالذكاء الاصطناعي
```

**Subject:**
```
إعادة ضبط كلمة المرور للتطبيق
```

**Message:**
```
مرحباً،

تلقينا طلباً لإعادة تعيين كلمة المرور لحسابك في المدقق العربي بالذكاء الاصطناعي.

البريد الإلكتروني: %EMAIL%

اضغط على الرابط أدناه لإعادة تعيين كلمة المرور:
%LINK%

هذا الرابط صالح لمدة ساعة واحدة فقط.

إذا لم تطلب إعادة تعيين كلمة المرور، يمكنك تجاهل هذا البريد الإلكتروني بأمان.

مع أطيب التحيات،
فريق المدقق العربي بالذكاء الاصطناعي

---
هذا بريد إلكتروني تلقائي، يرجى عدم الرد عليه.
```

### 3. تفعيل Firestore:
1. اذهب إلى: https://console.firebase.google.com/project/web-text-b4599/firestore
2. اضغط "Create database"
3. اختر "Start in test mode"

## 🌐 الرابط النهائي:
بعد النشر، سيكون الموقع متاح على:
**https://web-text-b4599.web.app**

## 🎯 الميزات المتاحة:
- ✅ مصحح الإملاء العربي
- ✅ المحرر الذكي بالذكاء الاصطناعي  
- ✅ الوضع الداكن
- ✅ تسجيل الدخول والتسجيل
- ✅ نسيت كلمة المرور (بعد تفعيل Authentication)
- ✅ حفظ النصوص (بعد تفعيل Firestore)
- ✅ تصميم متجاوب

## 🔍 استكشاف الأخطاء:
إذا واجهت مشاكل في النشر:
1. تأكد من تسجيل الدخول: `firebase login`
2. تحقق من المشروع: `firebase projects:list`
3. تأكد من تفعيل Hosting في Firebase Console
4. جرب النشر اليدوي عبر Firebase Console
